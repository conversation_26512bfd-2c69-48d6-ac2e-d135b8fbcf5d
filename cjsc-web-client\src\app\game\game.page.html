<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>
      Untitled Card Game
    </ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="leaveGame()">
        <ion-icon slot="icon-only" name="exit-outline"></ion-icon>
        Leave
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <!-- Loading state -->
  <div *ngIf="!gameState" class="loading-container ion-text-center">
    <p>Loading game...</p>
  </div>

  <!-- Waiting for players state -->
  <div *ngIf="gameState?.status === 'Waiting for Players'" class="lobby-container">
    <h1 class="ion-text-center">Waiting for Players</h1>
    <p class="ion-text-center">Room Code: <strong>{{ roomCode }}</strong></p>

    <ion-list>
      <ion-list-header>
        <ion-label>Players ({{ getPlayers().length }})</ion-label>
      </ion-list-header>
      <ion-item *ngFor="let player of getPlayers()" lines="none">
        <ion-label>{{ player }}</ion-label>
        <ion-note slot="end" *ngIf="player === playerName">You</ion-note>
      </ion-item>
    </ion-list>

    <ion-button
      *ngIf="isLeader && getPlayers().length >= 3"
      expand="block"
      (click)="startGame()"
      class="ion-margin-top">
      Start Game
    </ion-button>

    <p *ngIf="isLeader && getPlayers().length < 3" class="ion-text-center">
      Need at least 3 players to start the game.
    </p>
  </div>

  <!-- Waiting for Prompt state -->
  <div *ngIf="gameState?.status === 'Waiting for Prompt'" class="game-container">
    <h1 class="ion-text-center">Round {{ getCurrentRound() }}</h1>

    <div class="prompt-container ion-margin-vertical">
      <h2 class="ion-text-center">Waiting for prompt...</h2>
      <p class="ion-text-center">The game is selecting a prompt for this round</p>
    </div>

    <div class="judge-container ion-text-center" *ngIf="isJudge">
      <h3>You are the judge for this round</h3>
    </div>
  </div>

  <!-- Ready state -->
  <div *ngIf="gameState?.status === 'Ready'" class="game-container">
    <h1 class="ion-text-center">Round {{ getCurrentRound() }}</h1>

    <div class="prompt-container ion-margin-vertical">
      <h2 class="ion-text-center">Prompt:</h2>
      <p class="prompt ion-text-center">{{ getPrompt() }}</p>
    </div>

    <div *ngIf="isJudge" class="judge-container ion-text-center">
      <h3>You are the judge for this round</h3>
      <p>Wait for players to submit their responses.</p>
    </div>
  </div>

  <!-- Waiting for Player Responses state -->
  <div *ngIf="gameState?.status === 'Waiting for Player Responses'" class="game-container">
    <h1 class="ion-text-center">Round {{ getCurrentRound() }}</h1>

    <div class="prompt-container ion-margin-vertical">
      <h2 class="ion-text-center">Prompt:</h2>
      <p class="prompt ion-text-center">{{ getPrompt() }}</p>
    </div>

    <div *ngIf="isJudge" class="judge-container ion-text-center">
      <h3>You are the judge for this round</h3>
      <p>Wait for players to submit their responses.</p>

      <!-- Show how many players have submitted responses -->
      <p *ngIf="gameState?.submitted_responses">
        {{ gameState?.submitted_responses?.length || 0 }} /
        {{ (gameState?.players?.length || 0) - 1 }} players have submitted
      </p>
    </div>

    <div *ngIf="!isJudge" class="responses-container">
      <h3 class="ion-text-center">Choose your response:</h3>

      <div *ngIf="playerResponses.length === 0" class="ion-text-center ion-padding">
        <p>Waiting for responses to be assigned...</p>
      </div>

      <div *ngIf="hasSubmitted" class="ion-text-center ion-padding">
        <p>You have submitted your response.</p>
        <p>Waiting for other players to submit their responses...</p>

        <!-- Show how many players have submitted responses -->
        <p *ngIf="gameState?.submitted_responses">
          {{ gameState?.submitted_responses?.length || 0 }} /
          {{ (gameState?.players?.length || 0) - 1 }} players have submitted
        </p>
      </div>

      <div *ngIf="!hasSubmitted" class="response-options">
        <!-- Toggle between pre-defined cards and custom input -->
        <ion-segment [(ngModel)]="responseMode" (ionChange)="responseTypeChanged()">
          <ion-segment-button value="cards">
            <ion-label>Cards</ion-label>
          </ion-segment-button>
          <ion-segment-button value="custom">
            <ion-label>Custom</ion-label>
          </ion-segment-button>
        </ion-segment>

        <!-- Pre-defined cards list -->
        <ion-list *ngIf="responseMode === 'cards' && playerResponses.length > 0">
          <ion-item *ngFor="let response of playerResponses; let i = index"
                  [class.selected-response]="selectedResponseIndex === i"
                  (click)="selectResponse(i)"
                  lines="none"
                  button>
            <ion-label>{{ response }}</ion-label>
          </ion-item>
        </ion-list>

        <!-- Custom text input -->
        <div *ngIf="responseMode === 'custom'" class="custom-input-container ion-margin-vertical">
          <ion-item lines="none">
            <ion-label position="stacked">Enter your custom response:</ion-label>
            <ion-textarea
              [(ngModel)]="customResponse"
              placeholder="Type your response here..."
              [counter]="true"
              maxlength="150"
              rows="3"
              class="custom-response-input"></ion-textarea>
          </ion-item>
        </div>
      </div>

      <ion-button
        expand="block"
        [disabled]="(responseMode === 'cards' && (selectedResponseIndex < 0 || playerResponses.length === 0)) ||
                    (responseMode === 'custom' && !customResponse.trim())"
        (click)="submitResponse()"
        class="ion-margin-top"
        *ngIf="!hasSubmitted">
        Submit Response
      </ion-button>
    </div>
  </div>

  <!-- Ready for Judging state -->
  <div *ngIf="gameState?.status === 'Ready for Judging'" class="game-container">
    <h1 class="ion-text-center">Round {{ getCurrentRound() }}</h1>

    <div *ngIf="isJudge" class="judge-container">
      <h3 class="ion-text-center">You are the judge - select the best response:</h3>

      <ion-list>
        <ion-item *ngFor="let response of getSubmittedResponses(); let i = index"
                  [class.selected-response]="selectedWinnerIndex === i"
                  (click)="selectWinnerResponse(i)"
                  lines="none"
                  button>
          <ion-label>{{ response.text }}</ion-label>
        </ion-item>
      </ion-list>

      <ion-button
        expand="block"
        [disabled]="selectedWinnerIndex === null"
        (click)="selectWinner()"
        class="ion-margin-top">
        Select Winner
      </ion-button>

      <ion-button
        expand="block"
        (click)="skipJudging()"
        color="medium"
        class="ion-margin-top">
        Skip Judging (No Winner)
      </ion-button>
    </div>

    <div *ngIf="!isJudge" class="waiting-container ion-text-center">
      <h3>Waiting for judge to select a winner...</h3>
    </div>
  </div>

  <!-- Winner Chosen state -->
  <div *ngIf="gameState?.status === 'Winner Chosen'" class="game-container">
    <h1 class="ion-text-center">Round {{ getCurrentRound() }} Results</h1>

    <!-- Only show prompt container if getPrompt() returns a non-empty string -->
    <div *ngIf="getPrompt()" class="prompt-container ion-margin-vertical">
      <h2 class="ion-text-center">Prompt:</h2>
      <p class="prompt ion-text-center">{{ getPrompt() }}</p>
    </div>

    <div class="winner-container ion-text-center ion-margin-vertical">
      <h2>Winner: {{ gameState?.winner || 'No winner' }}</h2>
      <p class="winning-response">{{ gameState?.winning_response || 'No winning response' }}</p>
    </div>

    <ion-list>
      <ion-list-header>
        <ion-label>Scores</ion-label>
      </ion-list-header>
      <ion-item *ngFor="let player of getPlayers()" lines="none">
        <ion-label>{{ player }}</ion-label>
        <ion-note slot="end">{{ getPlayerScore(player) }}</ion-note>
      </ion-item>
    </ion-list>

    <div class="ion-text-center ion-margin-vertical">
      <p>Next round starting automatically in {{ nextRoundCountdown }} seconds...</p>
    </div>
  </div>

  <!-- Game Over state -->
  <div *ngIf="gameState?.status === 'Game Over'" class="game-container">
    <h1 class="ion-text-center">Game Over</h1>

    <div class="winner-container ion-text-center ion-margin-vertical">
      <h2>Game Winner: {{ gameState?.game_winner || getGameWinner() }}</h2>
      <p>Reached 5 points first!</p>
    </div>

    <ion-list>
      <ion-list-header>
        <ion-label>Final Scores</ion-label>
      </ion-list-header>
      <ion-item *ngFor="let player of getPlayers()" lines="none">
        <ion-label>{{ player }}</ion-label>
        <ion-note slot="end">{{ getPlayerScore(player) }}</ion-note>
      </ion-item>
    </ion-list>

    <div *ngIf="isLeader" class="leader-options">
      <h3 class="ion-text-center">What would you like to do?</h3>

      <ion-button
        expand="block"
        (click)="playAgain()"
        class="ion-margin-top">
        Play Again (Same Players)
      </ion-button>

      <ion-button
        expand="block"
        (click)="endGame()"
        color="danger"
        class="ion-margin-top">
        End Game
      </ion-button>
    </div>

    <div *ngIf="!isLeader" class="waiting-container ion-text-center">
      <h3>Waiting for leader to decide...</h3>
    </div>
  </div>
</ion-content>
